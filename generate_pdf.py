#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
木兰朵酒庄分析报告PDF生成器
使用weasyprint将HTML转换为PDF
"""

import os
import sys
from pathlib import Path

def install_dependencies():
    """安装必要的依赖包"""
    try:
        import weasyprint
        print("✓ weasyprint 已安装")
    except ImportError:
        print("正在安装 weasyprint...")
        os.system("pip install weasyprint")
        
    try:
        import weasyprint
        print("✓ weasyprint 安装成功")
        return True
    except ImportError:
        print("❌ weasyprint 安装失败，请手动安装：pip install weasyprint")
        return False

def generate_pdf():
    """生成PDF报告"""
    try:
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration
        
        # 获取当前目录
        current_dir = Path(__file__).parent
        html_file = current_dir / "report_template.html"
        pdf_file = current_dir / "木兰朵酒庄跨境电商出口海外机会分析报告.pdf"
        
        # 检查HTML文件是否存在
        if not html_file.exists():
            print(f"❌ HTML文件不存在: {html_file}")
            return False
            
        print(f"📄 正在读取HTML文件: {html_file}")
        
        # 创建字体配置
        font_config = FontConfiguration()
        
        # 额外的CSS样式
        additional_css = CSS(string='''
            @page {
                size: A4;
                margin: 2cm;
                @bottom-center {
                    content: "第 " counter(page) " 页";
                    font-size: 12px;
                    color: #666;
                }
            }
            
            body {
                font-family: "Microsoft YaHei", "SimSun", "DejaVu Sans", Arial, sans-serif;
            }
            
            .cover-page {
                page-break-after: always;
            }
            
            .toc {
                page-break-after: always;
            }
            
            h1 {
                page-break-before: always;
            }
            
            h1:first-of-type {
                page-break-before: auto;
            }
            
            .data-table {
                page-break-inside: avoid;
            }
            
            .swot-grid {
                page-break-inside: avoid;
            }
            
            .key-metrics {
                page-break-inside: avoid;
            }
        ''', font_config=font_config)
        
        print("🔄 正在生成PDF...")
        
        # 生成PDF
        html_doc = HTML(filename=str(html_file))
        html_doc.write_pdf(
            str(pdf_file),
            stylesheets=[additional_css],
            font_config=font_config
        )
        
        print(f"✅ PDF生成成功: {pdf_file}")
        print(f"📁 文件大小: {pdf_file.stat().st_size / 1024 / 1024:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF生成失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🍷 木兰朵酒庄分析报告PDF生成器")
    print("=" * 60)
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 生成PDF
    if generate_pdf():
        print("\n🎉 报告生成完成！")
        print("📋 报告包含以下内容：")
        print("   • 执行摘要")
        print("   • 木兰朵酒庄深度分析")
        print("   • 全球葡萄酒市场分析")
        print("   • 跨境电商环境分析")
        print("   • 竞争环境分析")
        print("   • SWOT分析")
        print("   • 市场进入策略建议")
        print("   • 运营实施计划")
        print("   • 财务预测与投资回报")
        print("   • 关键成功因素")
        print("   • 结论与建议")
    else:
        print("\n❌ 报告生成失败，请检查错误信息")

if __name__ == "__main__":
    main()
