#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
木兰朵酒庄分析报告PDF生成器 - 简化版本
使用浏览器打印功能生成PDF
"""

import os
import webbrowser
from pathlib import Path

def create_print_ready_html():
    """创建适合打印的HTML文件"""
    current_dir = Path(__file__).parent
    html_file = current_dir / "report_template.html"
    print_html_file = current_dir / "木兰朵酒庄分析报告_打印版.html"
    
    if not html_file.exists():
        print(f"❌ HTML文件不存在: {html_file}")
        return None
    
    # 读取原始HTML
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 添加打印专用CSS
    print_css = """
    <style media="print">
        @page {
            size: A4;
            margin: 1.5cm;
        }
        
        body {
            font-size: 12px;
            line-height: 1.4;
        }
        
        .cover-page {
            page-break-after: always;
        }
        
        .toc {
            page-break-after: always;
        }
        
        h1 {
            page-break-before: always;
            font-size: 20px;
        }
        
        h1:first-of-type {
            page-break-before: auto;
        }
        
        h2 {
            font-size: 16px;
            page-break-after: avoid;
        }
        
        h3 {
            font-size: 14px;
            page-break-after: avoid;
        }
        
        .data-table,
        .swot-grid,
        .key-metrics,
        .highlight-box {
            page-break-inside: avoid;
        }
        
        .chart-placeholder {
            height: 150px;
            font-size: 12px;
        }
        
        .footer {
            display: none;
        }
        
        /* 确保表格在打印时正确显示 */
        table {
            border-collapse: collapse;
            width: 100%;
        }
        
        th, td {
            border: 1px solid #333;
            padding: 8px;
            font-size: 11px;
        }
        
        /* SWOT网格在打印时的调整 */
        .swot-grid {
            display: block;
        }
        
        .swot-item {
            margin-bottom: 15px;
            page-break-inside: avoid;
        }
        
        /* 关键指标在打印时的调整 */
        .key-metrics {
            display: block;
        }
        
        .metric-card {
            display: inline-block;
            width: 30%;
            margin: 10px 1%;
            vertical-align: top;
        }
    </style>
    """
    
    # 在</head>前插入打印CSS
    html_content = html_content.replace('</head>', print_css + '\n</head>')
    
    # 写入新的HTML文件
    with open(print_html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return print_html_file

def open_in_browser(html_file):
    """在浏览器中打开HTML文件"""
    try:
        # 获取绝对路径
        abs_path = html_file.resolve()
        file_url = f"file:///{abs_path}"
        
        print(f"🌐 正在浏览器中打开: {file_url}")
        webbrowser.open(file_url)
        return True
    except Exception as e:
        print(f"❌ 打开浏览器失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🍷 木兰朵酒庄分析报告PDF生成器 - 简化版")
    print("=" * 60)
    
    # 创建打印版HTML
    print("📄 正在创建打印版HTML...")
    print_html_file = create_print_ready_html()
    
    if not print_html_file:
        print("❌ 创建打印版HTML失败")
        return
    
    print(f"✅ 打印版HTML创建成功: {print_html_file}")
    
    # 在浏览器中打开
    if open_in_browser(print_html_file):
        print("\n📋 使用说明：")
        print("1. 浏览器已打开报告页面")
        print("2. 按 Ctrl+P (Windows) 或 Cmd+P (Mac) 打开打印对话框")
        print("3. 选择 '另存为PDF' 或 '打印到PDF'")
        print("4. 设置页面选项：")
        print("   • 纸张大小：A4")
        print("   • 边距：默认或最小")
        print("   • 包含背景图形：建议勾选")
        print("5. 保存PDF文件")
        print("\n🎯 推荐文件名：木兰朵酒庄跨境电商出口海外机会分析报告.pdf")
    else:
        print(f"\n📁 请手动打开文件: {print_html_file}")
        print("然后按照上述说明生成PDF")

if __name__ == "__main__":
    main()
